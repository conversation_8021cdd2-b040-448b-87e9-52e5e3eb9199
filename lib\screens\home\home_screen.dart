import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/models/generation_mode.dart';
import 'package:novel_app/prompts/genre_prompts.dart';
import 'package:novel_app/models/character_card.dart';
import 'package:novel_app/models/character_type.dart';
import 'package:novel_app/screens/novel_detail_screen.dart';
import 'package:novel_app/screens/settings_screen.dart';

import 'package:novel_app/controllers/theme_controller.dart';
import 'package:novel_app/screens/genre_manager_screen.dart';
import 'package:novel_app/controllers/genre_controller.dart';
import 'package:novel_app/screens/module_repository_screen.dart';
import 'package:novel_app/controllers/style_controller.dart';
import 'package:novel_app/services/announcement_service.dart';
import 'package:novel_app/services/character_type_service.dart';
import 'package:novel_app/services/character_card_service.dart';
import 'package:novel_app/screens/character_card_list_screen.dart';
import 'package:novel_app/screens/knowledge_base_screen.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/widgets/common/animated_button.dart';
import 'package:novel_app/widgets/common/animated_card.dart';
import 'package:novel_app/widgets/common/animated_list_tile.dart';
import 'package:novel_app/screens/donate_screen.dart';
import 'package:novel_app/screens/ai_chat/daizong_ai_screen.dart';
import 'package:novel_app/screens/writing_style_package_screen.dart';
import 'package:novel_app/controllers/writing_style_package_controller.dart';
import 'package:novel_app/widgets/background_service_widget.dart';
import 'package:novel_app/widgets/themed_dropdown.dart';
import 'package:novel_app/controllers/auth_controller.dart';
import 'package:novel_app/services/enhanced_outline_import_service.dart';
import 'package:novel_app/screens/novel_continue/novel_selection_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final NovelController controller = Get.find<NovelController>();
  final ScrollController _outputScrollController = ScrollController();
  final TextEditingController _outlineEditController = TextEditingController();
  final TextEditingController _backgroundController = TextEditingController();

  // 添加折叠状态变量
  final RxBool _isGenreSelectorExpanded = true.obs;

  // 添加监听器管理
  Worker? _rawTextWorker;

  @override
  void initState() {
    super.initState();
    ever(controller.generationStage, _updateOutlineEditor);
    ever(controller.currentOutline,
        (_) => _updateOutlineEditor(controller.generationStage.value));

    // 监听背景字段的变化
    ever(controller.background, (String background) {
      if (_backgroundController.text != background) {
        _backgroundController.text = background;
      }
    });

    // 只创建一次 rawGeneratedOutlineText 监听器
    _rawTextWorker = ever(controller.rawGeneratedOutlineText, (String rawText) {
      if (controller.generationStage.value == GenerationStage.outlineReady &&
          controller.currentOutline.value == null &&
          rawText.isNotEmpty) {
        print(
            "[_updateOutlineEditor Listener] Received raw text, updating editor.");
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_outlineEditController.text != rawText) {
            _outlineEditController.text = rawText;
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _rawTextWorker?.dispose();
    _outputScrollController.dispose();
    _outlineEditController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  void _updateOutlineEditor(GenerationStage stage) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (stage == GenerationStage.outlineReady) {
        final outline = controller.currentOutline.value;
        final rawText = controller.rawGeneratedOutlineText.value;
        String newText = "";

        if (outline != null && outline.chapters.isNotEmpty) {
          // Case 1: Valid NovelOutline object exists (from import or previous step)
          print(
              "[_updateOutlineEditor] Stage outlineReady: Using NovelOutline object.");
          final buffer = StringBuffer();
          buffer.writeln('小说标题：${outline.novelTitle}');
          for (final chOutline in outline.chapters) {
            buffer.writeln(
                '\n第${chOutline.chapterNumber}章：${chOutline.chapterTitle}');
            buffer.writeln(chOutline.contentOutline);
          }
          newText = buffer.toString();
        } else if (rawText.isNotEmpty) {
          // Case 2: No NovelOutline, but raw generated text exists
          print(
              "[_updateOutlineEditor] Stage outlineReady: Using rawGeneratedOutlineText.");
          newText = rawText;
        } else {
          // Case 3: Neither exists, show fallback
          print(
              "[_updateOutlineEditor] Stage outlineReady: Using fallback text.");
          newText = "// 无法显示或解析大纲，请在此手动编辑或重新生成...";
        }

        // Update editor only if text changed
        if (_outlineEditController.text != newText) {
          print("[_updateOutlineEditor] Updating TextField content.");
          _outlineEditController.text = newText;
        }
      } else if (stage == GenerationStage.idle ||
          stage == GenerationStage.generatingOutline) {
        // Clear editor when not in outlineReady stage
        if (_outlineEditController.text.isNotEmpty) {
          print(
              "[_updateOutlineEditor] Clearing TextField content (Stage: $stage).");
          _outlineEditController.clear();
        }
      }
    });
  }

  @override
  void dispose() {
    _outputScrollController.dispose();
    _outlineEditController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();
    final authController = Get.find<AuthController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('岱宗文脉'),
        actions: [

          IconButton(
            icon: const Icon(Icons.smart_toy),
            tooltip: '岱宗AI',
            onPressed: () => Get.toNamed('/daizong_ai'),
          ),
          IconButton(
            icon: const Icon(Icons.build),
            tooltip: '工具广场',
            onPressed: () => Get.toNamed('/tools'),
          ),
          IconButton(
            icon: const Icon(Icons.apps),
            tooltip: '模块仓库',
            onPressed: () => Get.to(() => const ModuleRepositoryScreen()),
          ),
          IconButton(
            icon: const Icon(Icons.style),
            tooltip: '文风包管理',
            onPressed: () => Get.to(() => const WritingStylePackageScreen()),
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            tooltip: '使用教程',
            onPressed: () async {
              const tutorialUrl = 'https://ocnx9nxpa7xe.feishu.cn/wiki/XFYOwp4fXiynVukGG1JcmYZynYb?from=from_copylink';
              try {
                final Uri url = Uri.parse(tutorialUrl);
                if (await canLaunchUrl(url)) {
                  await launchUrl(url, mode: LaunchMode.externalApplication);
                } else {
                  // 如果无法打开，复制链接到剪贴板
                  await Clipboard.setData(const ClipboardData(text: tutorialUrl));
                  Get.snackbar(
                    '已复制教程链接',
                    '链接已复制到剪贴板，请在浏览器中打开',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.blue.withOpacity(0.1),
                    colorText: Colors.blue[700],
                  );
                }
              } catch (e) {
                // 出错时复制链接
                await Clipboard.setData(const ClipboardData(text: tutorialUrl));
                Get.snackbar(
                  '已复制教程链接',
                  '无法直接打开，链接已复制到剪贴板',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.orange.withOpacity(0.1),
                  colorText: Colors.orange[700],
                );
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Get.to(() => const SettingsScreen()),
          ),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
              ),
              child: const Text(
                '岱宗文脉',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.library_books),
              title: const Text('我的书库'),
              onTap: () {
                Get.back();
                Get.toNamed('/library');
              },
            ),
            ListTile(
              leading: const Icon(Icons.smart_toy),
              title: const Text('岱宗AI'),
              onTap: () {
                Get.back();
                Get.toNamed('/daizong_ai');
              },
            ),
            ListTile(
              leading: const Icon(Icons.refresh),
              title: const Text('刷新公告'),
              onTap: () {
                Get.back();
                Get.find<AnnouncementService>().refreshAnnouncement();
              },
            ),
            ListTile(
              leading: const Icon(Icons.help_outline),
              title: const Text('帮助'),
              onTap: () async {
                Get.back();
                const tutorialUrl = 'https://ocnx9nxpa7xe.feishu.cn/wiki/XFYOwp4fXiynVukGG1JcmYZynYb?from=from_copylink';
                try {
                  final Uri url = Uri.parse(tutorialUrl);
                  if (await canLaunchUrl(url)) {
                    await launchUrl(url, mode: LaunchMode.externalApplication);
                  } else {
                    await Clipboard.setData(const ClipboardData(text: tutorialUrl));
                    Get.snackbar(
                      '已复制教程链接',
                      '链接已复制到剪贴板，请在浏览器中打开',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.blue.withOpacity(0.1),
                      colorText: Colors.blue[700],
                    );
                  }
                } catch (e) {
                  await Clipboard.setData(const ClipboardData(text: tutorialUrl));
                  Get.snackbar(
                    '已复制教程链接',
                    '无法直接打开，链接已复制到剪贴板',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.orange.withOpacity(0.1),
                    colorText: Colors.orange[700],
                  );
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.book),
              title: const Text('知识库'),
              onTap: () {
                Get.back();
                Get.to(() => KnowledgeBaseScreen());
              },
            ),
            // ListTile(
            // leading: const Icon(Icons.coffee),
            //title: const Text('给我买杯咖啡'),
            //onTap: () {
            // Get.back();
            // Get.to(() => const DonateScreen());
            //},
            // ),
            const Divider(),
            // 用户登录/注册选项
            Obx(
              key: const ValueKey('auth_status_obx'),
              () {
                if (authController.isLoggedIn.value) {
                // 已登录状态
                return Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.person),
                      title: Text(
                          '用户: ${authController.currentUser.value?.username ?? ""}'),
                      subtitle: const Text('已登录'),
                      onTap: () {
                        Get.back();
                        Get.toNamed('/profile');
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.logout),
                      title: const Text('退出登录'),
                      onTap: () {
                        Get.back();
                        authController.logout();
                      },
                    ),
                  ],
                );
              } else {
                // 未登录状态
                return Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.login),
                      title: const Text('登录'),
                      onTap: () {
                        Get.back();
                        Get.toNamed('/login');
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.person_add),
                      title: const Text('注册'),
                      onTap: () {
                        Get.back();
                        Get.toNamed('/register');
                      },
                    ),
                  ],
                );
              }
            }),
            const Divider(),
            ListTile(
              leading: Obx(
                key: const ValueKey('theme_icon_obx'),
                () => Icon(
                  themeController.isDarkMode
                      ? Icons.light_mode
                      : Icons.dark_mode,
                ),
              ),
              title: const Text('暗黑模式'),
              trailing: Obx(
                key: const ValueKey('theme_switch_obx'),
                () => Switch(
                  value: themeController.isDarkMode,
                  onChanged: (_) => themeController.toggleTheme(),
                ),
              ),
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildGeneratorForm(),
            Obx(
              key: const ValueKey('generation_stage_obx'),
              () {
                final stage = controller.generationStage.value;
              if (stage == GenerationStage.outlineReady) {
                return _buildOutlineEditorWidget(
                    controller, _outlineEditController);
              } else if (stage == GenerationStage.detailedOutlineReady) {
                return _buildDetailedOutlineViewerWidget(controller, context);
              } else {
                return Container();
              }
            }),
            const SizedBox(height: 8),
            _buildGenerationStatus(),
            const SizedBox(height: 8),
            const BackgroundServiceWidget(),
            const SizedBox(height: 8),
            SizedBox(
              height: 300,
              child: _buildNovelList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneratorForm() {
    return Card(
      margin: EdgeInsets.zero,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.zero,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                Expanded(child: _TitleInput()),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  icon: const Icon(Icons.upload_file),
                  label: const Text('导入大纲'),
                  onPressed: () => _showImportOutlineDialog(context),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildGenreSelector(),
            const SizedBox(height: 16),
            _buildKnowledgeBaseToggle(),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '创作要求',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildCharacterSelector(),
                    const SizedBox(height: 12),
                    _buildTargetReaderSelector(),
                    const SizedBox(height: 12),
                    TextField(
                      controller: _backgroundController,
                      decoration: const InputDecoration(
                        labelText: '故事背景',
                        hintText: '例如：大学校园，现代都市',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: controller.updateBackground,
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      decoration: const InputDecoration(
                        labelText: '其他要求',
                        hintText: '其他具体要求，如情节发展、特殊设定等',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      onChanged: controller.updateOtherRequirements,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            GetX<NovelController>(
              builder: (controller) => ThemedDropdownButtonFormField<String>(
                value: controller.style.value,
                decoration: const InputDecoration(
                  labelText: '写作风格',
                ),
                items: Get.find<StyleController>()
                    .styles
                    .map((style) => DropdownMenuItem(
                          value: style.name,
                          child: Text(style.name),
                        ))
                    .toList(),
                onChanged: (value) => controller.updateStyle(value!),
              ),
            ),
            const SizedBox(height: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '文风包',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Obx(
                  key: const ValueKey('writing_style_package_obx'),
                  () {
                    // 获取文风包控制器
                    Get.find<WritingStylePackageController>();
                    final selectedPackage = controller.selectedWritingStyle.value;

                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                selectedPackage?.name ?? '未选择文风包',
                                style: TextStyle(
                                  color: selectedPackage != null
                                      ? Theme.of(context).primaryColor
                                      : Colors.grey,
                                ),
                              ),
                              Row(
                                children: [
                                  TextButton.icon(
                                    key: const ValueKey('writing_style_select_button'),
                                    icon: const Icon(Icons.style),
                                    label: Text(
                                      '选择',
                                      style: TextStyle(
                                        inherit: true,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                    onPressed: () =>
                                        _showWritingStyleSelector(context),
                                  ),
                                  if (selectedPackage != null)
                                    IconButton(
                                      key: const ValueKey('writing_style_clear_button'),
                                      icon: const Icon(Icons.clear),
                                      onPressed: () =>
                                          controller.selectWritingStyle(null),
                                    ),
                                ],
                              ),
                            ],
                          ),
                          if (selectedPackage != null) ...[
                            const Divider(),
                            Text(
                              '作者：${selectedPackage.author}',
                              style: const TextStyle(fontSize: 12),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              selectedPackage.description,
                              style: const TextStyle(fontSize: 12),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                    ),
                  );
                }),
              ],
            ),
            const SizedBox(height: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 添加精简生成模式切换
                Obx(
                  key: const ValueKey('simplified_mode_obx'),
                  () => Row(
                    children: [
                        Expanded(
                          child: CheckboxListTile(
                            title: const Text('精简生成'),
                            subtitle: const Text(
                                '使用相同的大纲和细纲生成模块，但只保存细纲记忆，利用嵌入模型来保证上下文连贯性，提高效率降低成本'),
                            value: controller.generationMode.value ==
                                GenerationMode.lightweight,
                            controlAffinity: ListTileControlAffinity.leading,
                            onChanged: (value) =>
                                controller.toggleGenerationMode(value ?? false
                                    ? GenerationMode.lightweight
                                    : GenerationMode.standard),
                          ),
                        ),
                      ],
                    )),
                Row(
                  children: [
                    const Text('章节数量：', style: TextStyle(fontSize: 14)),
                    Expanded(
                      child: Obx(
                        key: const ValueKey('chapters_slider_obx'),
                        () => Slider(
                          value: controller.totalChaptersRx.value.toDouble(),
                          min: 1,
                          max: 1000,
                          divisions: 999,
                          label: controller.totalChaptersRx.value.toString(),
                          onChanged: (value) =>
                              controller.updateTotalChapters(value.toInt()),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 80,
                      child: Obx(
                        key: const ValueKey('chapters_input_obx'),
                        () {
                          // 创建一个临时控制器，但保持文本与totalChaptersRx同步
                        final textController = TextEditingController(
                          text: controller.totalChaptersRx.value.toString(),
                        );
                        // 设置光标位置到末尾
                        textController.selection = TextSelection.fromPosition(
                          TextPosition(offset: textController.text.length),
                        );

                        return TextField(
                          keyboardType: TextInputType.number,
                          textAlign: TextAlign.center,
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.symmetric(horizontal: 8),
                            suffix: Text('章'),
                          ),
                          controller: textController,
                          onChanged: (value) {
                            final chapters = int.tryParse(value);
                            if (chapters != null && chapters > 0) {
                              controller.updateTotalChapters(chapters);
                            }
                          },
                        );
                      }),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 400),
                child: Column(
                  children: [
                Builder(
                  builder: (context) => Obx(() {
                    if (controller.isGenerating.value &&
                        (controller.generationStage.value ==
                                GenerationStage.generatingChapters ||
                            controller.generationStage.value ==
                                GenerationStage.generatingDetailedOutline)) {
                      if (controller.isPaused.value) {
                        return AnimatedButton(
                          onPressed: controller.checkAndContinueGeneration,
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.play_arrow),
                              SizedBox(width: 8),
                              Text('继续生成'),
                            ],
                          ),
                        );
                      } else {
                        return AnimatedButton(
                          onPressed: controller.stopGeneration,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.stop),
                              SizedBox(width: 8),
                              Text('停止生成'),
                            ],
                          ),
                        );
                      }
                    } else if (controller.generationStage.value ==
                        GenerationStage.idle) {
                      // 检查是否有当前小说，如果有，显示续写按钮
                      final currentNovel = controller.getCurrentNovel();
                      if (currentNovel != null &&
                          controller.currentOutline.value != null &&
                          currentNovel.chapters.isNotEmpty) {
                        return Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          alignment: WrapAlignment.center,
                          children: [
                            AnimatedButton(
                              onPressed: () {
                                // 根据小说类型生成大纲或直接生成小说
                                if (controller.novelType.value ==
                                    NovelType.shortNovel) {
                                  controller.generateNovel();
                                } else {
                                  controller.generateOutlineWrapper();
                                }
                              },
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(Icons.auto_stories),
                                  const SizedBox(width: 8),
                                  Obx(() => Text(controller.novelType.value ==
                                          NovelType.shortNovel
                                      ? '生成短篇小说'
                                      : '生成大纲')),
                                ],
                              ),
                            ),
                            AnimatedButton(
                              onPressed: controller.continueNovelGeneration,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                              ),
                              child: const Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(Icons.update),
                                  SizedBox(width: 8),
                                  Text('续写小说'),
                                ],
                              ),
                            ),
                          ],
                        );
                      } else {
                        return AnimatedButton(
                          onPressed: () {
                            // 根据小说类型生成大纲或直接生成小说
                            if (controller.novelType.value ==
                                NovelType.shortNovel) {
                              controller.generateNovel();
                            } else {
                              controller.generateOutlineWrapper();
                            }
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(Icons.auto_stories),
                              const SizedBox(width: 8),
                              Obx(() => Text(controller.novelType.value ==
                                      NovelType.shortNovel
                                  ? '生成短篇小说'
                                  : '生成大纲')),
                            ],
                          ),
                        );
                      }
                    } else if (controller.generationStage.value ==
                        GenerationStage.outlineReady) {
                      // 大纲导入后显示“开始生成”按钮
                      return AnimatedButton(
                        onPressed: controller.startGeneration,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          foregroundColor:
                              Theme.of(context).colorScheme.onPrimary,
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.play_arrow),
                            SizedBox(width: 8),
                            Text('开始生成'),
                          ],
                        ),
                      );
                    } else if (controller.generationStage.value ==
                        GenerationStage.generationComplete) {
                      // 生成完成后显示续写按钮
                      return AnimatedButton(
                        onPressed: controller.continueNovelGeneration,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.update),
                            SizedBox(width: 8),
                            Text('续写小说'),
                          ],
                        ),
                      );
                    } else {
                      return Container();
                    }
                  }),
                ),
                Builder(
                  builder: (context) => Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    alignment: WrapAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => Get.to(() => NovelSelectionScreen()),
                        icon: const Icon(Icons.edit_note),
                        label: const Text('续写小说'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () => _showStartNewNovelDialog(context),
                        icon: const Icon(Icons.add),
                        label: const Text('开始新小说'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).colorScheme.secondary,
                          foregroundColor:
                              Theme.of(context).colorScheme.onSecondary,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                        ),
                      ),
                    ],
                  ),
                ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showImportOutlineDialog(BuildContext context) {
    final textController = TextEditingController();
    final RxBool isAnalyzing = false.obs;
    final RxBool isDetailedOutline = false.obs; // 是否为细纲模式
    final enhancedImportService = Get.find<EnhancedOutlineImportService>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title:
            const Text('智能导入大纲', style: TextStyle(fontWeight: FontWeight.bold)),
        content: Container(
          width: double.maxFinite,
          constraints: BoxConstraints(
            maxHeight:
                MediaQuery.of(context).size.height * 0.7, // 限制最大高度为屏幕高度的70%
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '请输入您的小说大纲，支持任意格式：',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  '• AI将自动识别您的大纲内容\n'
                  '• 请确保大纲里有“第n章”这种分割大纲的语句，不然ai会按照段落分割\n'
                  '• 支持任意格式的文本输入\n'
                  '• 大纲里最好不要包含特殊符号，避免章节识别误差\n'
                  '• 可以是章节大纲、情节概述或其他形式\n'
                  '• 系统会自动分析并组织成章节结构\n'
                  '• 建议导入大纲时使用小模型，导入更快，比如阿里的qwen2.5-7b-instruct-1m模型；gpt的mini系列；gemini的flash系列',
                  style: TextStyle(fontSize: 13),
                ),
                const SizedBox(height: 12),
                // 导入模式选择
                const Text(
                  '请选择导入模式：',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Obx(() => Column(
                      children: [
                        RadioListTile<bool>(
                          title: const Text('导入普通大纲（导入后需要生成细纲）'),
                          subtitle: const Text('适用于简单的章节概要，导入后需要生成详细细纲',
                              style: TextStyle(fontSize: 12)),
                          value: false,
                          groupValue: isDetailedOutline.value,
                          onChanged: isAnalyzing.value
                              ? null
                              : (value) => isDetailedOutline.value = value!,
                          dense: true,
                          contentPadding:
                              const EdgeInsets.symmetric(horizontal: 0),
                        ),
                        RadioListTile<bool>(
                          title: const Text('导入细纲（导入后直接可以生成）'),
                          subtitle: const Text('适用于已经很详细的章节内容，导入后可直接生成小说',
                              style: TextStyle(fontSize: 12)),
                          value: true,
                          groupValue: isDetailedOutline.value,
                          onChanged: isAnalyzing.value
                              ? null
                              : (value) => isDetailedOutline.value = value!,
                          dense: true,
                          contentPadding:
                              const EdgeInsets.symmetric(horizontal: 0),
                        ),
                      ],
                    )),
                const SizedBox(height: 12),
                const Text(
                  'AI将智能解析您的内容，生成结构化的大纲',
                  style: TextStyle(
                      fontStyle: FontStyle.italic, color: Colors.blue),
                ),
                const SizedBox(height: 12),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Obx(() => Stack(
                        children: [
                          TextField(
                            controller: textController,
                            maxLines: 10,
                            decoration: const InputDecoration(
                              hintText: '在此粘贴或输入您的小说大纲...',
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.all(12),
                            ),
                            enabled: !isAnalyzing.value,
                          ),
                          if (isAnalyzing.value)
                            Container(
                              height: 240,
                              alignment: Alignment.center,
                              color: Colors.black
                                  .withAlpha(13), // 替换 withOpacity(0.05)
                              child: Obx(() => Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const CircularProgressIndicator(),
                                      const SizedBox(height: 16),
                                      Text(
                                        enhancedImportService
                                                .importStatus.value.isEmpty
                                            ? 'AI正在智能解析大纲...'
                                            : enhancedImportService
                                                .importStatus.value,
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold),
                                      ),
                                      const SizedBox(height: 8),
                                      Obx(() => LinearProgressIndicator(
                                            value: enhancedImportService
                                                .importProgress.value,
                                          )),
                                      const SizedBox(height: 8),
                                      Obx(() => Text(
                                            '已处理 ${enhancedImportService.processedChapters.value}/${enhancedImportService.totalChapters.value} 章',
                                            style: const TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey),
                                          )),
                                    ],
                                  )),
                            ),
                        ],
                      )),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: isAnalyzing.value ? null : () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          Obx(() => ElevatedButton(
                onPressed: isAnalyzing.value
                    ? null
                    : () async {
                        if (textController.text.trim().isEmpty) {
                          Get.snackbar('提示', '请输入大纲内容');
                          return;
                        }

                        if (controller.title.value.isEmpty) {
                          Get.snackbar('提示', '请先输入小说标题');
                          return;
                        }

                        isAnalyzing.value = true;

                        // 使用 Get.back() 替代 Navigator.pop(context)
                        try {
                          final result = await controller.importOutline(
                            textController.text,
                            isDetailedOutline: isDetailedOutline.value,
                          );

                          isAnalyzing.value = false;

                          // 使用 Get.back() 关闭对话框，不需要 context
                          Get.back();

                          if (result) {
                            controller.generationStage.value = isDetailedOutline
                                    .value
                                ? GenerationStage.detailedOutlineReady // 细纲模式
                                : GenerationStage.outlineReady; // 普通大纲模式

                            final modeText =
                                isDetailedOutline.value ? '细纲' : '大纲';
                            Get.snackbar(
                              '成功',
                              'AI智能解析$modeText成功！共 ${controller.currentOutline.value?.chapters.length ?? 0} 章',
                              backgroundColor: Colors.green.shade100,
                              colorText: Colors.green.shade800,
                              snackPosition: SnackPosition.BOTTOM,
                              duration: const Duration(seconds: 3),
                            );
                          }
                        } catch (e) {
                          isAnalyzing.value = false;

                          // 使用 Get.back() 关闭对话框，不需要 context
                          Get.back();
                          Get.snackbar('导入失败', '导入大纲时发生错误: $e');
                        }
                      },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    isAnalyzing.value
                        ? Container(
                            width: 16,
                            height: 16,
                            margin: const EdgeInsets.only(right: 8),
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.check, size: 16),
                    const SizedBox(width: 4),
                    Obx(() =>
                        Text('AI智能导入${isDetailedOutline.value ? '细纲' : '大纲'}')),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildGenreSelector() {
    final genreController = Get.find<GenreController>();

    return Card(
      elevation: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 收窄的AppBar样式标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.7),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.category,
                  size: 18,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '选择类型（最多5个）',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ),
                // 已选类型数量显示
                Obx(() => controller.selectedGenres.isNotEmpty
                    ? Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.secondary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${controller.selectedGenres.length}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSecondary,
                          ),
                        ),
                      )
                    : const SizedBox()),
                const SizedBox(width: 8),
                // 折叠/展开按钮
                Obx(() => InkWell(
                      onTap: () => _isGenreSelectorExpanded.toggle(),
                      borderRadius: BorderRadius.circular(20),
                      child: Padding(
                        padding: const EdgeInsets.all(4),
                        child: AnimatedRotation(
                          turns: _isGenreSelectorExpanded.value ? 0.5 : 0,
                          duration: const Duration(milliseconds: 200),
                          child: Icon(
                            Icons.keyboard_arrow_down,
                            size: 20,
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                      ),
                    )),
              ],
            ),
          ),
          // 可折叠的内容区域
          Obx(() => AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                height: _isGenreSelectorExpanded.value ? null : 0,
                child: _isGenreSelectorExpanded.value
                    ? Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              children: genreController.categories
                                  .map((category) => _buildCategorySection(category))
                                  .toList(),
                            ),
                          ),
                          _buildSelectedGenresSection(),
                        ],
                      )
                    : const SizedBox(),
              )),
        ],
      ),
    );
  }

  // 构建分类区域
  Widget _buildCategorySection(category) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.5),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Text(
              category.name,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSecondaryContainer,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: _buildGenreGrid(category.genres),
          ),
        ],
      ),
    );
  }

  // 构建类型网格
  Widget _buildGenreGrid(List genres) {
    // 计算每行显示的数量，根据屏幕宽度动态调整
    final screenWidth = MediaQuery.of(context).size.width;
    final crossAxisCount = screenWidth > 600 ? 5 : 4;

    return Obx(() => GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: 2.2,
        crossAxisSpacing: 6,
        mainAxisSpacing: 6,
      ),
      itemCount: genres.length,
      itemBuilder: (context, index) {
        final genre = genres[index];
        return _buildGenreChip(genre);
      },
    ));
  }

  // 构建类型芯片
  Widget _buildGenreChip(genre) {
    final isSelected = controller.selectedGenres.contains(genre.name);
    final isDisabled = !isSelected && controller.selectedGenres.length >= 5;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: FilterChip(
        label: Text(
          genre.name,
          style: TextStyle(
            fontSize: 11,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        onSelected: isDisabled ? null : (_) => controller.toggleGenre(genre.name),
        backgroundColor: isDisabled
            ? Colors.grey.withOpacity(0.1)
            : null,
        selectedColor: Theme.of(context).colorScheme.primaryContainer,
        checkmarkColor: Theme.of(context).colorScheme.primary,
        side: BorderSide(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : isDisabled
                  ? Colors.grey.withOpacity(0.3)
                  : Theme.of(context).colorScheme.outline.withOpacity(0.5),
          width: 0.8,
        ),
        elevation: isSelected ? 1 : 0,
        pressElevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        labelPadding: EdgeInsets.zero,
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        visualDensity: VisualDensity.compact,
      ),
    );
  }

  // 构建已选类型区域
  Widget _buildSelectedGenresSection() {
    return Obx(() => controller.selectedGenres.isNotEmpty
        ? Container(
            margin: const EdgeInsets.only(top: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondaryContainer.withOpacity(0.2),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 16,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '已选类型 (${controller.selectedGenres.length}/5)',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: controller.selectedGenres
                      .map((genre) => Chip(
                            key: ValueKey('selected_genre_$genre'),
                            label: Text(
                              genre,
                              style: const TextStyle(fontSize: 11),
                            ),
                            deleteIcon: const Icon(Icons.close, size: 14),
                            onDeleted: () => controller.toggleGenre(genre),
                            backgroundColor: Theme.of(context).colorScheme.surface,
                            side: BorderSide(
                              color: Theme.of(context).colorScheme.secondary,
                              width: 0.8,
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 6),
                            labelPadding: const EdgeInsets.only(left: 4),
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            visualDensity: VisualDensity.compact,
                          ))
                      .toList(),
                ),
              ],
            ),
          )
        : const SizedBox());
  }

  Widget _buildCharacterSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.people, size: 18, color: Colors.grey),
            const SizedBox(width: 8),
            const Text(
              '选择角色',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Obx(() => controller.selectedCharacterTypes.isNotEmpty
                ? Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.secondary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${controller.selectedCharacterTypes.length}',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSecondary,
                      ),
                    ),
                  )
                : const SizedBox()),
          ],
        ),
        const SizedBox(height: 12),
        // 角色选择按钮
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _showCharacterSelectorDialog(),
            icon: const Icon(Icons.add_circle_outline, size: 18),
            label: Obx(() => Text(
              controller.selectedCharacterTypes.isEmpty
                  ? '点击选择角色类型'
                  : '已选择 ${controller.selectedCharacterTypes.length} 种角色类型',
              style: const TextStyle(fontSize: 14),
            )),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              side: BorderSide(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        // 已选角色类型显示
        Obx(() => controller.selectedCharacterTypes.isNotEmpty
            ? _buildSelectedCharacterTypesDisplay()
            : const SizedBox()),
      ],
    );
  }

  // 构建已选角色类型显示
  Widget _buildSelectedCharacterTypesDisplay() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '已选角色类型',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: controller.selectedCharacterTypes.map((type) {
              final hasCards = controller.selectedCharacterCards[type.id] != null;
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Color(int.parse(type.color, radix: 16)).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Color(int.parse(type.color, radix: 16)),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleAvatar(
                      radius: 8,
                      backgroundColor: Color(int.parse(type.color, radix: 16)),
                      child: hasCards
                          ? const Icon(Icons.check, color: Colors.white, size: 12)
                          : null,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      type.name,
                      style: const TextStyle(fontSize: 11),
                    ),
                    if (hasCards) ...[
                      const SizedBox(width: 4),
                      Text(
                        '(1)',
                        style: TextStyle(
                          fontSize: 10,
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () => controller.toggleCharacterType(type),
                      child: Icon(
                        Icons.close,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  // 显示角色选择弹窗
  void _showCharacterSelectorDialog() {
    final characterTypeService = Get.find<CharacterTypeService>();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            constraints: const BoxConstraints(maxHeight: 600),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 弹窗标题
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.people,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          '选择角色类型',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Icon(
                          Icons.close,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ],
                  ),
                ),
                // 角色类型列表
                Flexible(
                  child: Obx(() => ListView.builder(
                    shrinkWrap: true,
                    itemCount: characterTypeService.characterTypes.length,
                    itemBuilder: (context, index) {
                      final type = characterTypeService.characterTypes[index];
                      final isSelected = controller.selectedCharacterTypes.contains(type);
                      final hasCards = controller.selectedCharacterCards[type.id] != null;

                      return _buildCharacterTypeItem(type, isSelected, hasCards);
                    },
                  )),
                ),
                // 底部按钮
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('取消'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('确定'),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 构建角色类型项
  Widget _buildCharacterTypeItem(CharacterType type, bool isSelected, bool hasCards) {
    return Obx(() {
      final currentIsSelected = controller.selectedCharacterTypes.contains(type);
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color: currentIsSelected
              ? Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3)
              : null,
          borderRadius: BorderRadius.circular(8),
          border: currentIsSelected
              ? Border.all(color: Theme.of(context).colorScheme.primary.withOpacity(0.5))
              : null,
        ),
        child: ListTile(
        leading: Obx(() {
          final currentIsSelected = controller.selectedCharacterTypes.contains(type);
          return CircleAvatar(
            backgroundColor: Color(int.parse(type.color, radix: 16)),
            child: currentIsSelected
                ? const Icon(Icons.check, color: Colors.white, size: 18)
                : Text(
                    type.name.substring(0, 1),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          );
        }),
        title: Obx(() {
          final currentIsSelected = controller.selectedCharacterTypes.contains(type);
          return Text(
            type.name,
            style: TextStyle(
              fontWeight: currentIsSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          );
        }),
        subtitle: Text(
          type.description,
          style: const TextStyle(fontSize: 12),
        ),
        trailing: Obx(() {
          final currentHasCards = controller.selectedCharacterCards[type.id] != null;
          final currentIsSelected = controller.selectedCharacterTypes.contains(type);

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (currentHasCards) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.secondary,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '1张卡片',
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.onSecondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
              ],
              if (currentIsSelected)
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showCharacterCardSelector(type);
                  },
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    minimumSize: Size.zero,
                  ),
                  child: Text(
                    currentHasCards ? '编辑卡片' : '选择卡片',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
            ],
          );
        }),
        onTap: () => controller.toggleCharacterType(type),
        ),
      );
    });
  }

  String _buildCharacterSummary(CharacterCard card) {
    final parts = <String>[];
    if (card.gender.isNotEmpty) {
      parts.add(card.gender);
    }
    if (card.age.isNotEmpty) {
      parts.add('${card.age}岁');
    }
    if (card.personalityTraits.isNotEmpty) {
      parts.add(card.personalityTraits);
    }
    return parts.join(' · ');
  }

  Widget _buildTargetReaderSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '目标读者',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('男性向'),
                    value: '男性向',
                    groupValue: controller.targetReader.value,
                    onChanged: (value) => controller.updateTargetReader(value!),
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('女性向'),
                    value: '女性向',
                    groupValue: controller.targetReader.value,
                    onChanged: (value) => controller.updateTargetReader(value!),
                  ),
                ),
              ],
            )),
      ],
    );
  }

  void _showCharacterCardSelector(CharacterType type) {
    final characterCardService = Get.find<CharacterCardService>();

    // 确保角色类型已经初始化
    // 不需要初始化，因为Map默认返回null

    // 创建本地选择状态，初始化为已选择的角色卡片
    final selectedCards = <CharacterCard>[].obs;
    final currentCard = controller.selectedCharacterCards[type.id];
    if (currentCard != null) {
      selectedCards.add(currentCard);
    }

    Get.dialog(
      AlertDialog(
        title: Text('选择${type.name}角色卡片'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: Column(
            children: [
              // 显示已选择的角色数量
              Obx(() => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text(
                  '已选择 ${selectedCards.length} 个角色',
                  style: TextStyle(
                    color: Theme.of(Get.context!).primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )),
              Expanded(
                child: Obx(() {
                  final cards = characterCardService.getAllCards();
                  if (cards.isEmpty) {
                    return const Center(
                      child: Text('还没有创建角色卡片'),
                    );
                  }
                  return Obx(() => ListView.builder(
                    shrinkWrap: true,
                    itemCount: cards.length,
                    itemBuilder: (context, index) {
                      final card = cards[index];
                      final isSelected = selectedCards.any((c) => c.id == card.id);
                      return ListTile(
                        key: ValueKey('list_tile_${card.id}'),
                        title: Text(card.name),
                        subtitle: Text(_buildCharacterSummary(card)),
                        leading: Checkbox(
                          value: isSelected,
                          onChanged: (bool? value) {
                            _toggleCardSelection(selectedCards, card, value == true);
                          },
                        ),
                        onTap: () {
                          final isCurrentlySelected = selectedCards.any((c) => c.id == card.id);
                          _toggleCardSelection(selectedCards, card, !isCurrentlySelected);
                        },
                      );
                    },
                  ));
                }),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.to(() => CharacterCardListScreen());
            },
            child: const Text('创建新角色'),
          ),
          TextButton(
            onPressed: () {
              // 更新选择的角色卡片
              if (selectedCards.isNotEmpty) {
                controller.selectedCharacterCards[type.id] = selectedCards.first;
              } else {
                controller.selectedCharacterCards.remove(type.id);
              }
              // 强制触发响应式更新
              controller.selectedCharacterCards.refresh();
              Get.back();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _toggleCardSelection(RxList<CharacterCard> selectedCards, CharacterCard card, bool shouldSelect) {
    if (shouldSelect) {
      if (!selectedCards.any((c) => c.id == card.id)) {
        selectedCards.add(card);
      }
    } else {
      selectedCards.removeWhere((c) => c.id == card.id);
    }
  }

  String _getCharacterCardDisplayText(String typeId) {
    final card = controller.selectedCharacterCards[typeId];
    if (card == null) {
      return '选择角色卡片';
    } else {
      return card.name;
    }
  }

  Widget _buildGenerationStatus() {
    return GetX<NovelController>(
      builder: (controller) {
        if (!controller.isGenerating.value) {
          return const SizedBox();
        }
        return Card(
          elevation: 4.0,
          margin: EdgeInsets.zero,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '生成进度',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withAlpha(51),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Text('正在生成', style: TextStyle(fontSize: 12)),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                LinearProgressIndicator(
                  value: controller.generationProgress.value,
                  minHeight: 8,
                  backgroundColor: Colors.grey.withAlpha(51),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
                const SizedBox(height: 8),
                Text(
                  controller.generationStatus.value,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  '实时输出:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 300,
                  decoration: BoxDecoration(
                    color: Colors.black.withAlpha(13),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.withAlpha(77)),
                  ),
                  child: Obx(() {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (_outputScrollController.hasClients) {
                        _outputScrollController.jumpTo(
                          _outputScrollController.position.maxScrollExtent,
                        );
                      }
                    });

                    return SingleChildScrollView(
                      controller: _outputScrollController,
                      padding: const EdgeInsets.all(12),
                      child: Text(
                        controller.realtimeOutput.value.isEmpty
                            ? '等待生成内容...'
                            : controller.realtimeOutput.value,
                        style: const TextStyle(
                          fontSize: 14,
                          height: 1.5,
                        ),
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildNovelList() {
    return GetX<NovelController>(
      builder: (controller) {
        if (controller.novels.isEmpty) {
          return const Center(
            child: Text('还没有生成任何小说'),
          );
        }
        return ListView.builder(
          itemCount: controller.novels.length,
          itemBuilder: (context, index) {
            final novel = controller.novels[index];
            return AnimatedCard(
              margin: EdgeInsets.zero,
              onTap: () => Get.to(() => NovelDetailScreen(novel: novel)),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                novel.title,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                novel.genre,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showStartNewNovelDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('开始新小说'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('这将清除所有当前设置、生成状态和缓存。'),
            const SizedBox(height: 16),
            const Text('请选择小说类型：'),
            const SizedBox(height: 8),
            Obx(() => Column(
                  children: NovelType.values
                      .map((type) => RadioListTile<NovelType>(
                            title: Text(
                              type.displayName,
                              style: TextStyle(
                                color: type == NovelType.shortNovel
                                    ? Colors.grey
                                    : null,
                              ),
                            ),
                            subtitle: type == NovelType.shortNovel
                                ? const Text(
                                    '功能重构中，暂时不可用',
                                    style: TextStyle(
                                      color: Colors.orange,
                                      fontSize: 12,
                                    ),
                                  )
                                : null,
                            value: type,
                            groupValue: controller.novelType.value,
                            onChanged: type == NovelType.shortNovel
                                ? null // 禁用短篇小说选项
                                : (value) {
                                    if (value != null) {
                                      controller.setNovelType(value);
                                    }
                                  },
                            contentPadding: EdgeInsets.zero,
                          ))
                      .toList(),
                )),
            Obx(() {
              if (controller.novelType.value == NovelType.shortNovel) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    const Text('选择字数：'),
                    const SizedBox(height: 8),
                    ThemedDropdownButtonFormField<ShortNovelWordCount>(
                      value: controller.shortNovelWordCount.value,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: ShortNovelWordCount.values
                          .map(
                            (wordCount) => DropdownMenuItem(
                              value: wordCount,
                              child: Text(wordCount.displayName),
                            ),
                          )
                          .toList(),
                      onChanged: (value) {
                        if (value != null) {
                          controller.setShortNovelWordCount(value);
                        }
                      },
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller.startNewNovel();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Widget _buildKnowledgeBaseToggle() {
    final knowledgeBaseController = Get.find<KnowledgeBaseController>();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '知识库辅助',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Obx(() => Switch(
                      value: knowledgeBaseController.useKnowledgeBase.value,
                      onChanged: (value) {
                        knowledgeBaseController.useKnowledgeBase.value = value;
                        knowledgeBaseController.saveSettings();
                        if (value &&
                            knowledgeBaseController.selectedDocIds.isEmpty) {
                          Get.to(() => KnowledgeBaseScreen());
                        }
                      },
                    )),
              ],
            ),
            Obx(() {
              if (!knowledgeBaseController.useKnowledgeBase.value) {
                return const SizedBox.shrink();
              }

              final selectedCount =
                  knowledgeBaseController.selectedDocIds.length;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        selectedCount > 0
                            ? '已选择 $selectedCount 个知识文档'
                            : '未选择知识文档',
                        style: TextStyle(
                          color: selectedCount > 0 ? Colors.green : Colors.red,
                        ),
                      ),
                      TextButton.icon(
                        icon: const Icon(Icons.edit),
                        label: const Text('管理'),
                        onPressed: () => Get.to(() => KnowledgeBaseScreen()),
                      ),
                    ],
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  void _showWritingStyleSelector(BuildContext context) {
    final writingStyleController = Get.find<WritingStylePackageController>();
    final novelController = Get.find<NovelController>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择文风包'),
        content: SizedBox(
          width: double.maxFinite,
          child: Obx(() {
            final packages = writingStyleController.packages;
            if (packages.isEmpty) {
              return const Center(
                child: Text('还没有创建文风包'),
              );
            }
            return ListView.builder(
              shrinkWrap: true,
              itemCount: packages.length,
              itemBuilder: (context, index) {
                final package = packages[index];
                return ListTile(
                  title: Text(package.name),
                  subtitle: Text(package.description),
                  trailing: novelController.selectedWritingStyle.value?.id ==
                          package.id
                      ? const Icon(Icons.check, color: Colors.green)
                      : null,
                  onTap: () {
                    novelController.selectWritingStyle(package);
                    Navigator.pop(context);
                  },
                );
              },
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Get.to(() => const WritingStylePackageScreen());
            },
            child: const Text('管理文风包'),
          ),
        ],
      ),
    );
  }

  Widget _buildOutlineEditorWidget(
      NovelController controller, TextEditingController outlineController) {
    return AnimatedCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('生成的大纲',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text('请检查并修改下方生成的大纲，确认无误后点击"生成细纲"。',
                style: TextStyle(color: Colors.grey[600])),
            const SizedBox(height: 16),
            TextField(
              controller: outlineController,
              maxLines: 15,
              decoration: InputDecoration(
                hintText: '编辑大纲内容...',
                border: const OutlineInputBorder(),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: AnimatedButton(
                    onPressed: () {
                      // 生成细纲
                      final editedOutlineText = outlineController.text;
                      controller
                          .generateDetailedOutlineFromEdited(editedOutlineText);
                    },
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.checklist_rtl, size: 18),
                        SizedBox(width: 8),
                        Text('生成细纲'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {
                    // 生成大纲
                    controller.generateOutlineWrapper();
                  },
                  child: const Text('重新生成大纲'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedOutlineViewerWidget(
      NovelController controller, BuildContext context) {
    final detailedOutline = controller.currentOutline.value;
    if (detailedOutline == null || detailedOutline.chapters.isEmpty) {
      return AnimatedCard(
          child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text('错误：无法显示细纲。',
                  style: TextStyle(color: Colors.red[700]))));
    }

    return AnimatedCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('生成的细纲',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text('请检查下方生成的章节细纲（情节梗概），确认无误后点击"生成小说章节"。',
                style: TextStyle(color: Colors.grey[600])),
            const SizedBox(height: 16),
            Container(
              constraints: const BoxConstraints(maxHeight: 400),
              decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8)),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: detailedOutline.chapters.length,
                itemBuilder: (context, index) {
                  final chapter = detailedOutline.chapters[index];
                  return ExpansionTile(
                    title: Text(
                        '第${chapter.chapterNumber}章: ${chapter.chapterTitle}',
                        style: const TextStyle(fontWeight: FontWeight.w600)),
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 8.0),
                        child: MarkdownBody(
                          data: chapter.contentOutline,
                          selectable: true,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: AnimatedButton(
                    onPressed: () {
                      controller.generationStage.value =
                          GenerationStage.generatingChapters;
                      // 生成章节
                      controller.startGeneration();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.auto_fix_high, size: 18),
                        SizedBox(width: 8),
                        Text('生成小说章节'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {
                    controller.generationStage.value =
                        GenerationStage.outlineReady;
                  },
                  child: const Text('返回修改大纲'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _TitleInput extends StatefulWidget {
  @override
  _TitleInputState createState() => _TitleInputState();
}

class _TitleInputState extends State<_TitleInput> {
  late final TextEditingController _titleController;
  final NovelController _novelController = Get.find<NovelController>();

  @override
  void initState() {
    super.initState();
    _titleController =
        TextEditingController(text: _novelController.title.value);
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (_titleController.text != _novelController.title.value) {
        _titleController.text = _novelController.title.value;
      }
      return TextField(
        controller: _titleController,
        decoration: const InputDecoration(
          labelText: '小说标题',
          hintText: '请输入小说标题',
        ),
        onChanged: _novelController.updateTitle,
      );
    });
  }
}
// 添加重新开始按钮
